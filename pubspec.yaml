name: moroccanaccounting
description: A mobile app for learning Moroccan accounting principles
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.5.0+12

environment:
  sdk: '>=3.1.0 <4.0.0'
  flutter: ">=3.10.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations: # Added for internationalization
    sdk: flutter         # Added for internationalization
  window_manager: ^0.5.1
  animations: ^2.0.8
  flutter_highlight: ^0.7.0
  flutter_riverpod: ^2.5.1
  # State Management
  provider: ^6.1.1
  excel: ^4.0.6
  path_provider: ^2.1.1
  open_file: ^3.3.2
  io: ^1.0.4  # For File operations
  
  # Add the missing packages
  just_audio: ^0.10.4
  just_audio_web: ^0.4.9  # Web support for just_audio
  confetti: ^0.8.0
  freezed_annotation: ^3.1.0 # Downgraded for compatibility
  json_annotation: ^4.9.0  # Required by freezed
  
  # What's New Dialog dependencies

  
  # Utilities
  hive: ^2.2.3 # Added for local storage
  hive_flutter: ^1.1.0 # Added for Flutter integration with Hive
  # path_provider is already listed above
  shared_preferences: ^2.5.2
  url_launcher: ^6.2.2
  
  # Accessibility and Offline Dependencies
  flutter_tts: ^3.8.5 # Text-to-speech capabilities
  speech_to_text: ^6.6.0 # Voice input support
  connectivity_plus: ^5.0.2 # Network status monitoring
  device_info_plus: ^9.1.1 # Platform-specific accessibility features
  flutter_cache_manager: ^3.3.1 # Advanced caching capabilities
  dio: ^5.4.0 # Network requests with offline support
  google_fonts: ^6.2.1
  intl: ^0.20.2 # Updated for compatibility with flutter_localizations
  logging: ^1.2.0
  fl_chart: ^1.0.0
  syncfusion_flutter_charts: ^30.1.41 # Updated for intl 0.19.0 compatibility
  syncfusion_flutter_core: ^30.1.41 # Updated for intl 0.19.0 compatibility
  syncfusion_flutter_treemap: ^30.1.41 # Updated for intl 0.19.0 compatibility
  flutter_animate: ^4.5.0
  lottie: ^3.3.0
  flutter_markdown: ^0.7.1 # Added for rendering exam statements
  hive_generator: ^2.0.1 # Added for Hive code generation
  freezed: ^3.0.0-0.0.dev # Downgraded for compatibility with hive_generator
  json_serializable: ^6.7.1 # Downgraded for compatibility with hive_generator
  flutter_svg: ^2.0.17
  package_info_plus: ^8.3.0
  collection: ^1.18.0
  markdown: ^7.2.2
  share_plus: ^10.1.2
  pdf: ^3.10.4
  printing: ^5.11.0
  path: ^1.8.3
  file_picker: ^8.1.2  # Added missing file_picker dependency

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0 # Added standard Flutter lints
  build_runner: ^2.4.15 # Moved from dependencies
  mockito: ^5.4.4  # Added for testing
  # Add dev dependencies for code generation

# The following section is specific to Flutter packages.
flutter:
  generate: true
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/exams/ # Added for Exam data
    - assets/exams/comptabilite_generale/
    - assets/exams/comptabilite_generale/ibn_zohr/
    - assets/exams/comptabilite_generale/ibn_zohr/global/
    - assets/exams/comptabilite_generale/generated/ # Added generated exams
    - assets/exams/comptabilite_analytique/
    - assets/exams/comptabilite_analytique/mohamed_1er/
    - assets/exams/comptabilite_analytique/mohamed_1er/pluridisciplinaire/
    - assets/exams/fiscalite/ # Added for Fiscalité exams
    - assets/exams/fiscalite/cas_pratique/ # Added for Fiscalité case studies
    # General Accounting (Comptabilité Générale)
    - assets/compta_generale/
    - assets/compta_generale/comptabilite_generale.json
    - assets/compta_generale/introduction.json
    - assets/compta_generale/documents_comptables.json
    - assets/compta_generale/plan_comptable.json
    - assets/compta_generale/gestion_stocks.json
    
    # Add sounds directory
    - assets/sounds/
    
    # Company Accounting (Comptabilité des Sociétés)
    - assets/compta_soc/fusion.json
    - assets/compta_soc/fusion_comptabilisation.json
    
    # Existing JSON files
    - assets/is/presentation.json
    - assets/is/base_imposable.json
    - assets/is/obligations.json
    - assets/is/sanctions.json
    - assets/is/exercices.json
    - assets/is/exercices_avances.json
    - assets/is/is_rein_deduction_2025.json
    - assets/is/is_taux_2025.json
    - assets/plan_comptable.json
    - assets/ir/presentation.json
    - assets/ir/bareme.json
    - assets/ir/revenus.json
    - assets/ir/deductions.json
    - assets/ir/cnss.json
    - assets/ir/obligations.json
    - assets/ir/exercices.json
    - assets/ir/exemples.json
    - assets/ir/sanctions.json
    - assets/tva/tva_2025.json
    - assets/tva/ras_tva.json
    - assets/tva/calcul_tva.json
    - assets/immobilisations/evaluation_data.json
    - assets/provisions/overview.json
    - assets/provisions/calculator.json
    - assets/provisions/exercices.json
    - assets/immobilisations/acquisition_onereux.json
    - assets/immobilisations/acquisition_echange.json
    - assets/immobilisations/production_interne.json
    - assets/transport/transport_types.json
    - assets/transport/exercises.json
    - assets/transport/comptabilisation.json
    - assets/droits_enregistrement/taux_2025.json
    - assets/droits_enregistrement/sanctions.json
    - assets/droits_enregistrement/nouveautes_2025.json
    - assets/droits_enregistrement/obligations.json
    - assets/droits_enregistrement/exemples.json
    - assets/droits_enregistrement/tabs.json
    - assets/compta_analytique/comptabilite_analytique.json
    - assets/compta_analytique/methodes_calcul.json
    - assets/compta_analytique/centres_analyse.json
    - assets/compta_analytique/couts_complets.json
    - assets/compta_analytique/seuil_rentabilite.json
    - assets/tva/exercices_tva.json
    - assets/tva/remboursement_tva_2025.json
    - assets/quiz/quiz_data.json

    # Advanced Calculators
    - assets/calculators/
    - assets/calculators/financial_ratios_benchmarks.json
    - assets/calculators/depreciation_presets.json
    - assets/calculators/tax_optimization_strategies.json

    # Liasse Fiscale
    - assets/liasse_fiscale/
    - assets/liasse_fiscale/tabs.json
    - assets/liasse_fiscale/guide_liasse_2025.json
    - assets/liasse_fiscale/checklist_documents_2025.json
    - assets/liasse_fiscale/obligations_fiscales_2025.json

    # Agriculture
    - assets/agriculture/comptabilite_agricole.json
    - assets/agriculture/ir_agricole_2025.json
    - assets/agriculture/is_agricole_2025.json
    - assets/agriculture/tva_agricole_2025.json
    - assets/agriculture/pratiques_agricoles_2025.json
    - assets/agriculture/exercices_agricoles_2025.json
    - assets/agriculture/calculateurs_agricoles_2025.json
    
    # Other assets
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/lottie/ # Added for Lottie animations
    
    # Directories
    - assets/
    - assets/is/
    - assets/ir/
    - assets/tva/
    - assets/immobilisations/
    - assets/compta_soc/
    - assets/compta_analytique/
    - assets/liasse_fiscale/
    
    # Added from the code block
    - assets/sounds/incorrect.mp3
    - assets/sounds/incorrect.webm
    - assets/sounds/incorrect.ogg
    - assets/sounds/correct.mp3
    - assets/sounds/correct.webm
    - assets/sounds/correct.ogg
 

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
