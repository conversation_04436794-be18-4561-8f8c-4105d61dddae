import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:convert';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';

import '../../services/accessibility_service.dart';
import '../../models/accessibility/accessibility_preferences.dart';

/// Comprehensive accessibility settings screen with controls for all accessibility features,
/// testing tools, presets, and configuration management.
class AccessibilitySettingsScreen extends StatefulWidget {
  const AccessibilitySettingsScreen({super.key});

  @override
  State<AccessibilitySettingsScreen> createState() => _AccessibilitySettingsScreenState();
}

class _AccessibilitySettingsScreenState extends State<AccessibilitySettingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AccessibilityService _accessibilityService;
  
  // Focus nodes for keyboard navigation
  final Map<String, FocusNode> _focusNodes = {};
  
  // Testing tools state
  bool _showFocusIndicators = false;
  bool _screenReaderSimulation = false;
  Color _testColor1 = Colors.black;
  Color _testColor2 = Colors.white;
  double _contrastRatio = 21.0;
  
  // Assessment questionnaire state
  int _currentQuestionIndex = 0;
  Map<String, dynamic> _assessmentAnswers = {};
  bool _showAssessment = false;
  
  // Preview text for font scaling
  final String _previewText = 'Exemple de texte pour prévisualiser la taille de police';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _accessibilityService = Provider.of<AccessibilityService>(context, listen: false);
    _initializeFocusNodes();
    _calculateContrastRatio();
  }

  void _initializeFocusNodes() {
    final nodeIds = [
      'font_slider', 'high_contrast_toggle', 'screen_reader_toggle',
      'keyboard_nav_toggle', 'reduced_motion_toggle', 'navigation_mode_dropdown',
      'vision_preset', 'motor_preset', 'cognitive_preset', 'reset_button',
      'export_button', 'import_button', 'assessment_button'
    ];
    
    for (final id in nodeIds) {
      _focusNodes[id] = FocusNode();
      _accessibilityService.registerFocusNode(id, _focusNodes[id]!);
    }
  }

  void _calculateContrastRatio() {
    final luminance1 = _testColor1.computeLuminance();
    final luminance2 = _testColor2.computeLuminance();
    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;
    setState(() {
      _contrastRatio = (lighter + 0.05) / (darker + 0.05);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    for (final focusNode in _focusNodes.values) {
      focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AccessibilityService>(
      builder: (context, accessibilityService, child) {
        return Scaffold(
          appBar: AppBar(
            title: Semantics(
              header: true,
              child: Text(
                'Paramètres d\'accessibilité',
                style: TextStyle(
                  fontSize: accessibilityService.getScaledFontSize(20),
                ),
              ),
            ),
            actions: [
              IconButton(
                focusNode: _focusNodes['help_button'],
                onPressed: _showAccessibilityHelp,
                tooltip: 'Aide sur l\'accessibilité',
                icon: const Icon(Icons.help_outline),
              ),
            ],
            bottom: TabBar(
              controller: _tabController,
              isScrollable: true,
              tabs: [
                _buildTab('Général', Icons.settings),
                _buildTab('Préréglages', Icons.tune),
                _buildTab('Tests', Icons.science),
                _buildTab('Évaluation', Icons.quiz),
                _buildTab('Gestion', Icons.import_export),
              ],
            ),
          ),
          body: TabBarView(
            controller: _tabController,
            children: [
              _buildGeneralSettingsTab(accessibilityService),
              _buildPresetsTab(accessibilityService),
              _buildTestingToolsTab(accessibilityService),
              _buildAssessmentTab(accessibilityService),
              _buildManagementTab(accessibilityService),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTab(String label, IconData icon) {
    return Tab(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 8),
          Text(label),
        ],
      ),
    );
  }

  // GENERAL SETTINGS TAB
  Widget _buildGeneralSettingsTab(AccessibilityService service) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Taille de police'),
          _buildFontSizeSection(service),
          const SizedBox(height: 24),
          
          _buildSectionHeader('Affichage'),
          _buildDisplaySection(service),
          const SizedBox(height: 24),
          
          _buildSectionHeader('Navigation'),
          _buildNavigationSection(service),
          const SizedBox(height: 24),
          
          _buildSectionHeader('Lecteur d\'écran'),
          _buildScreenReaderSection(service),
          const SizedBox(height: 24),
          
          _buildSectionHeader('Mouvement et animations'),
          _buildMotionSection(service),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Semantics(
      header: true,
      child: Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: Text(
          title,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontSize: _accessibilityService.getScaledFontSize(18),
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildFontSizeSection(AccessibilityService service) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Taille de police: ${(service.fontScale * 100).round()}%',
                    style: TextStyle(
                      fontSize: service.getScaledFontSize(16),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Semantics(
                  label: 'Réinitialiser la taille de police',
                  button: true,
                  child: IconButton(
                    onPressed: () => service.updateFontScale(1.0),
                    icon: const Icon(Icons.refresh),
                    tooltip: 'Réinitialiser',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Semantics(
              label: service.generateContentLabel(
                'Curseur de taille de police, valeur actuelle ${(service.fontScale * 100).round()}%',
                'slider'
              ),
              child: Slider(
                focusNode: _focusNodes['font_slider'],
                value: service.fontScale,
                min: 0.8,
                max: 2.0,
                divisions: 12,
                onChanged: (value) async {
                  await service.updateFontScale(value);
                  await service.provideHapticFeedback();
                },
                semanticFormatterCallback: (value) => '${(value * 100).round()}%',
              ),
            ),
            const SizedBox(height: 16),
            _buildFontPreview(service),
          ],
        ),
      ),
    );
  }

  Widget _buildFontPreview(AccessibilityService service) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).dividerColor),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Aperçu:',
            style: TextStyle(
              fontSize: service.getScaledFontSize(12),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _previewText,
            style: TextStyle(
              fontSize: service.getScaledFontSize(16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDisplaySection(AccessibilityService service) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildToggleListTile(
              focusNode: _focusNodes['high_contrast_toggle']!,
              title: 'Mode contraste élevé',
              subtitle: 'Améliore la lisibilité avec des couleurs contrastées',
              value: service.isHighContrastEnabled,
              onChanged: (value) async {
                await service.toggleHighContrast();
                await service.provideHapticFeedback();
              },
              icon: Icons.contrast,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationSection(AccessibilityService service) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildToggleListTile(
              focusNode: _focusNodes['keyboard_nav_toggle']!,
              title: 'Navigation clavier',
              subtitle: 'Active la navigation avec les touches du clavier',
              value: service.isKeyboardNavigationEnabled,
              onChanged: (value) async {
                await service.updateKeyboardNavigation(value);
                await service.provideHapticFeedback();
              },
              icon: Icons.keyboard,
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.navigation),
              title: Text(
                'Mode de navigation',
                style: TextStyle(
                  fontSize: service.getScaledFontSize(16),
                ),
              ),
              subtitle: Text(
                'Choisir le mode de navigation préféré',
                style: TextStyle(
                  fontSize: service.getScaledFontSize(14),
                ),
              ),
              trailing: Semantics(
                label: 'Sélectionner le mode de navigation',
                child: DropdownButton<PreferredNavigationMode>(
                  focusNode: _focusNodes['navigation_mode_dropdown'],
                  value: service.preferences.preferredNavigationMode,
                  onChanged: (PreferredNavigationMode? value) async {
                    if (value != null) {
                      await service.updateNavigationMode(value);
                      await service.provideHapticFeedback();
                    }
                  },
                  items: PreferredNavigationMode.values.map((mode) {
                    return DropdownMenuItem(
                      value: mode,
                      child: Text(_getNavigationModeLabel(mode)),
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScreenReaderSection(AccessibilityService service) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildToggleListTile(
              focusNode: _focusNodes['screen_reader_toggle']!,
              title: 'Lecteur d\'écran',
              subtitle: 'Active les annonces vocales et la synthèse vocale',
              value: service.isScreenReaderEnabled,
              onChanged: (value) async {
                await service.enableScreenReader(value);
                await service.provideHapticFeedback();
              },
              icon: Icons.record_voice_over,
            ),
            if (service.isScreenReaderEnabled) ...[
              const Divider(),
              ListTile(
                leading: const Icon(Icons.volume_up),
                title: Text(
                  'Test de synthèse vocale',
                  style: TextStyle(
                    fontSize: service.getScaledFontSize(16),
                  ),
                ),
                subtitle: Text(
                  'Tester la lecture vocale',
                  style: TextStyle(
                    fontSize: service.getScaledFontSize(14),
                  ),
                ),
                trailing: ElevatedButton(
                  onPressed: () => service.announceToScreenReader(
                    'Test de synthèse vocale. Le lecteur d\'écran fonctionne correctement.'
                  ),
                  child: const Text('Tester'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMotionSection(AccessibilityService service) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildToggleListTile(
              focusNode: _focusNodes['reduced_motion_toggle']!,
              title: 'Mouvement réduit',
              subtitle: 'Réduit les animations et transitions',
              value: service.isReducedMotionEnabled,
              onChanged: (value) async {
                await service.updateReducedMotion(value);
                await service.provideHapticFeedback();
              },
              icon: Icons.motion_photos_off,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleListTile({
    required FocusNode focusNode,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
  }) {
    return Semantics(
      label: _accessibilityService.generateContentLabel(
        '$title, ${value ? 'activé' : 'désactivé'}',
        'checkbox'
      ),
      child: SwitchListTile(
        focusNode: focusNode,
        secondary: Icon(icon),
        title: Text(
          title,
          style: TextStyle(
            fontSize: _accessibilityService.getScaledFontSize(16),
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: _accessibilityService.getScaledFontSize(14),
          ),
        ),
        value: value,
        onChanged: onChanged,
      ),
    );
  }

  // PRESETS TAB
  Widget _buildPresetsTab(AccessibilityService service) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Préréglages d\'accessibilité'),
          Text(
            'Configurations optimisées pour différents besoins d\'accessibilité',
            style: TextStyle(
              fontSize: service.getScaledFontSize(14),
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
          const SizedBox(height: 24),
          
          _buildPresetCard(
            service,
            'Vision',
            'Optimisé pour les déficiences visuelles',
            Icons.visibility,
            _focusNodes['vision_preset']!,
            () => _applyVisionPreset(service),
          ),
          
          _buildPresetCard(
            service,
            'Motricité',
            'Optimisé pour les difficultés motrices',
            Icons.touch_app,
            _focusNodes['motor_preset']!,
            () => _applyMotorPreset(service),
          ),
          
          _buildPresetCard(
            service,
            'Cognition',
            'Optimisé pour les difficultés cognitives',
            Icons.psychology,
            _focusNodes['cognitive_preset']!,
            () => _applyCognitivePreset(service),
          ),
          
          const SizedBox(height: 24),
          
          Center(
            child: ElevatedButton.icon(
              focusNode: _focusNodes['reset_button'],
              onPressed: () => _showResetDialog(service),
              icon: const Icon(Icons.restore),
              label: Text(
                'Réinitialiser aux valeurs par défaut',
                style: TextStyle(
                  fontSize: service.getScaledFontSize(16),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPresetCard(
    AccessibilityService service,
    String title,
    String description,
    IconData icon,
    FocusNode focusNode,
    VoidCallback onApply,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 32),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: service.getScaledFontSize(18),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: service.getScaledFontSize(14),
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildPresetDetails(service, title),
            const SizedBox(height: 16),
            Align(
              alignment: Alignment.centerRight,
              child: ElevatedButton(
                focusNode: focusNode,
                onPressed: onApply,
                child: Text(
                  'Appliquer',
                  style: TextStyle(
                    fontSize: service.getScaledFontSize(16),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPresetDetails(AccessibilityService service, String presetType) {
    Map<String, String> details = {};
    
    switch (presetType) {
      case 'Vision':
        details = {
          'Taille de police': '150%',
          'Contraste élevé': 'Activé',
          'Lecteur d\'écran': 'Activé',
          'Mouvement réduit': 'Activé',
        };
        break;
      case 'Motricité':
        details = {
          'Navigation clavier': 'Activée',
          'Cibles tactiles': 'Agrandies',
          'Mouvement réduit': 'Activé',
          'Retour haptique': 'Réduit',
        };
        break;
      case 'Cognition':
        details = {
          'Mouvement réduit': 'Activé',
          'Interface simplifiée': 'Activée',
          'Temps d\'interaction': 'Prolongé',
          'Aide contextuelle': 'Activée',
        };
        break;
    }
    
    return Column(
      children: details.entries.map((entry) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            children: [
              Icon(Icons.check_circle_outline, size: 16, color: Colors.green),
              const SizedBox(width: 8),
              Text(
                '${entry.key}: ${entry.value}',
                style: TextStyle(
                  fontSize: service.getScaledFontSize(14),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  // TESTING TOOLS TAB
  Widget _buildTestingToolsTab(AccessibilityService service) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Outils de test d\'accessibilité'),
          
          _buildContrastCheckerTool(service),
          const SizedBox(height: 24),
          
          _buildFocusIndicatorTester(service),
          const SizedBox(height: 24),
          
          _buildScreenReaderSimulator(service),
          const SizedBox(height: 24),
          
          _buildKeyboardNavigationTester(service),
        ],
      ),
    );
  }

  Widget _buildContrastCheckerTool(AccessibilityService service) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Vérificateur de contraste',
              style: TextStyle(
                fontSize: service.getScaledFontSize(18),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        'Couleur 1',
                        style: TextStyle(
                          fontSize: service.getScaledFontSize(14),
                        ),
                      ),
                      const SizedBox(height: 8),
                      GestureDetector(
                        onTap: () => _pickColor(true),
                        child: Container(
                          height: 50,
                          decoration: BoxDecoration(
                            color: _testColor1,
                            border: Border.all(),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        'Couleur 2',
                        style: TextStyle(
                          fontSize: service.getScaledFontSize(14),
                        ),
                      ),
                      const SizedBox(height: 8),
                      GestureDetector(
                        onTap: () => _pickColor(false),
                        child: Container(
                          height: 50,
                          decoration: BoxDecoration(
                            color: _testColor2,
                            border: Border.all(),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _testColor2,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(),
              ),
              child: Text(
                'Exemple de texte',
                style: TextStyle(
                  color: _testColor1,
                  fontSize: service.getScaledFontSize(16),
                ),
              ),
            ),
            const SizedBox(height: 16),
            _buildContrastResults(),
          ],
        ),
      ),
    );
  }

  Widget _buildContrastResults() {
    final isAAA = _contrastRatio >= 7.0;
    final isAA = _contrastRatio >= 4.5;
    
    return Column(
      children: [
        Text(
          'Ratio de contraste: ${_contrastRatio.toStringAsFixed(2)}:1',
          style: TextStyle(
            fontSize: _accessibilityService.getScaledFontSize(16),
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            _buildComplianceIndicator('WCAG AA', isAA),
            const SizedBox(width: 16),
            _buildComplianceIndicator('WCAG AAA', isAAA),
          ],
        ),
      ],
    );
  }

  Widget _buildComplianceIndicator(String standard, bool compliant) {
    return Row(
      children: [
        Icon(
          compliant ? Icons.check_circle : Icons.cancel,
          color: compliant ? Colors.green : Colors.red,
          size: 20,
        ),
        const SizedBox(width: 4),
        Text(
          standard,
          style: TextStyle(
            fontSize: _accessibilityService.getScaledFontSize(14),
            color: compliant ? Colors.green : Colors.red,
          ),
        ),
      ],
    );
  }

  Widget _buildFocusIndicatorTester(AccessibilityService service) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Testeur d\'indicateurs de focus',
              style: TextStyle(
                fontSize: service.getScaledFontSize(18),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: Text(
                'Afficher les indicateurs de focus',
                style: TextStyle(
                  fontSize: service.getScaledFontSize(16),
                ),
              ),
              subtitle: Text(
                'Visualise les indicateurs de focus pour tous les éléments',
                style: TextStyle(
                  fontSize: service.getScaledFontSize(14),
                ),
              ),
              value: _showFocusIndicators,
              onChanged: (value) {
                setState(() {
                  _showFocusIndicators = value;
                });
              },
            ),
            if (_showFocusIndicators) ...[
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildFocusTestButton('Bouton 1'),
                  _buildFocusTestButton('Bouton 2'),
                  _buildFocusTestButton('Bouton 3'),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFocusTestButton(String label) {
    return Focus(
      child: Builder(
        builder: (context) {
          final isFocused = Focus.of(context).hasFocus;
          return Container(
            decoration: BoxDecoration(
              border: isFocused && _showFocusIndicators
                  ? Border.all(color: Colors.blue, width: 3)
                  : null,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ElevatedButton(
              onPressed: () {},
              child: Text(label),
            ),
          );
        },
      ),
    );
  }

  Widget _buildScreenReaderSimulator(AccessibilityService service) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Simulateur de lecteur d\'écran',
              style: TextStyle(
                fontSize: service.getScaledFontSize(18),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: Text(
                'Mode simulation',
                style: TextStyle(
                  fontSize: service.getScaledFontSize(16),
                ),
              ),
              subtitle: Text(
                'Simule l\'expérience d\'un lecteur d\'écran',
                style: TextStyle(
                  fontSize: service.getScaledFontSize(14),
                ),
              ),
              value: _screenReaderSimulation,
              onChanged: (value) {
                setState(() {
                  _screenReaderSimulation = value;
                });
                if (value) {
                  service.announceToScreenReader('Mode simulation du lecteur d\'écran activé');
                }
              },
            ),
            if (_screenReaderSimulation) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.black87,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Mode simulation actif - Les éléments seront annoncés lors de la navigation',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: service.getScaledFontSize(14),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildKeyboardNavigationTester(AccessibilityService service) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Testeur de navigation clavier',
              style: TextStyle(
                fontSize: service.getScaledFontSize(18),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Raccourcis clavier disponibles:',
              style: TextStyle(
                fontSize: service.getScaledFontSize(16),
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            ..._buildKeyboardShortcutsList(service),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildKeyboardShortcutsList(AccessibilityService service) {
    final shortcuts = [
      {'key': 'Tab / Shift+Tab', 'action': 'Navigation entre éléments'},
      {'key': 'Entrée / Espace', 'action': 'Activer un élément'},
      {'key': 'Flèches', 'action': 'Navigation directionnelle'},
      {'key': 'Échap', 'action': 'Fermer / Retour'},
      {'key': 'Ctrl + Plus/Moins', 'action': 'Ajuster la taille de police'},
      {'key': 'Ctrl + Shift + C', 'action': 'Basculer le contraste élevé'},
    ];

    return shortcuts.map((shortcut) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                shortcut['key']!,
                style: TextStyle(
                  fontSize: service.getScaledFontSize(12),
                  fontFamily: 'monospace',
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                shortcut['action']!,
                style: TextStyle(
                  fontSize: service.getScaledFontSize(14),
                ),
              ),
            ),
          ],
        ),
      );
    }).toList();
  }

  // ASSESSMENT TAB
  Widget _buildAssessmentTab(AccessibilityService service) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Évaluation des besoins d\'accessibilité'),
          Text(
            'Répondez à ce questionnaire pour obtenir des recommandations personnalisées',
            style: TextStyle(
              fontSize: service.getScaledFontSize(14),
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
          const SizedBox(height: 24),
          
          if (!_showAssessment) ...[
            Center(
              child: ElevatedButton.icon(
                focusNode: _focusNodes['assessment_button'],
                onPressed: () {
                  setState(() {
                    _showAssessment = true;
                    _currentQuestionIndex = 0;
                    _assessmentAnswers.clear();
                  });
                },
                icon: const Icon(Icons.quiz),
                label: Text(
                  'Commencer l\'évaluation',
                  style: TextStyle(
                    fontSize: service.getScaledFontSize(16),
                  ),
                ),
              ),
            ),
          ] else ...[
            _buildAssessmentQuestion(service),
          ],
        ],
      ),
    );
  }

  Widget _buildAssessmentQuestion(AccessibilityService service) {
    final questions = _getAssessmentQuestions();
    
    if (_currentQuestionIndex >= questions.length) {
      return _buildAssessmentResults(service);
    }
    
    final question = questions[_currentQuestionIndex];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            LinearProgressIndicator(
              value: (_currentQuestionIndex + 1) / questions.length,
            ),
            const SizedBox(height: 16),
            Text(
              'Question ${_currentQuestionIndex + 1} sur ${questions.length}',
              style: TextStyle(
                fontSize: service.getScaledFontSize(14),
                color: Theme.of(context).textTheme.bodyMedium?.color,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              question['question'],
              style: TextStyle(
                fontSize: service.getScaledFontSize(18),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...question['options'].map<Widget>((option) {
              return RadioListTile<String>(
                title: Text(
                  option,
                  style: TextStyle(
                    fontSize: service.getScaledFontSize(16),
                  ),
                ),
                value: option,
                groupValue: _assessmentAnswers[question['id']],
                onChanged: (value) {
                  setState(() {
                    _assessmentAnswers[question['id']] = value;
                  });
                },
              );
            }).toList(),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (_currentQuestionIndex > 0)
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _currentQuestionIndex--;
                      });
                    },
                    child: const Text('Précédent'),
                  )
                else
                  const SizedBox(),
                ElevatedButton(
                  onPressed: _assessmentAnswers[question['id']] != null
                      ? () {
                          setState(() {
                            _currentQuestionIndex++;
                          });
                        }
                      : null,
                  child: Text(_currentQuestionIndex == questions.length - 1 ? 'Terminer' : 'Suivant'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssessmentResults(AccessibilityService service) {
    final recommendations = _generateRecommendations();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recommandations personnalisées',
              style: TextStyle(
                fontSize: service.getScaledFontSize(20),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...recommendations.map((recommendation) {
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: Icon(recommendation['icon']),
                  title: Text(
                    recommendation['title'],
                    style: TextStyle(
                      fontSize: service.getScaledFontSize(16),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  subtitle: Text(
                    recommendation['description'],
                    style: TextStyle(
                      fontSize: service.getScaledFontSize(14),
                    ),
                  ),
                  trailing: ElevatedButton(
                    onPressed: recommendation['action'],
                    child: const Text('Appliquer'),
                  ),
                ),
              );
            }).toList(),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _showAssessment = false;
                        _currentQuestionIndex = 0;
                        _assessmentAnswers.clear();
                      });
                    },
                    child: const Text('Recommencer'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _exportAssessmentResults(),
                    child: const Text('Exporter'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // MANAGEMENT TAB
  Widget _buildManagementTab(AccessibilityService service) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Gestion des paramètres'),
          
          _buildExportImportSection(service),
          const SizedBox(height: 24),
          
          _buildBackupSection(service),
          const SizedBox(height: 24),
          
          _buildDiagnosticsSection(service),
        ],
      ),
    );
  }

  Widget _buildExportImportSection(AccessibilityService service) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Exporter / Importer',
              style: TextStyle(
                fontSize: service.getScaledFontSize(18),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    focusNode: _focusNodes['export_button'],
                    onPressed: () => _exportSettings(service),
                    icon: const Icon(Icons.upload),
                    label: Text(
                      'Exporter',
                      style: TextStyle(
                        fontSize: service.getScaledFontSize(16),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    focusNode: _focusNodes['import_button'],
                    onPressed: () => _importSettings(service),
                    icon: const Icon(Icons.download),
                    label: Text(
                      'Importer',
                      style: TextStyle(
                        fontSize: service.getScaledFontSize(16),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupSection(AccessibilityService service) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sauvegarde automatique',
              style: TextStyle(
                fontSize: service.getScaledFontSize(18),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.backup),
              title: Text(
                'Dernière sauvegarde',
                style: TextStyle(
                  fontSize: service.getScaledFontSize(16),
                ),
              ),
              subtitle: Text(
                'Aujourd\'hui à 14:30',
                style: TextStyle(
                  fontSize: service.getScaledFontSize(14),
                ),
              ),
              trailing: TextButton(
                onPressed: () => _createBackup(service),
                child: const Text('Sauvegarder'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiagnosticsSection(AccessibilityService service) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Diagnostics',
              style: TextStyle(
                fontSize: service.getScaledFontSize(18),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildDiagnosticItem(
              'Lecteur d\'écran',
              service.isTtsAvailable ? 'Disponible' : 'Non disponible',
              service.isTtsAvailable,
            ),
            _buildDiagnosticItem(
              'Navigation clavier',
              service.isKeyboardNavigationEnabled ? 'Activée' : 'Désactivée',
              service.isKeyboardNavigationEnabled,
            ),
            _buildDiagnosticItem(
              'Retour haptique',
              'Supporté',
              true,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _runDiagnostics(service),
              child: const Text('Exécuter les diagnostics'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiagnosticItem(String label, String status, bool isOk) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isOk ? Icons.check_circle : Icons.error,
            color: isOk ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: _accessibilityService.getScaledFontSize(14),
              ),
            ),
          ),
          Text(
            status,
            style: TextStyle(
              fontSize: _accessibilityService.getScaledFontSize(14),
              color: isOk ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  // HELPER METHODS

  String _getNavigationModeLabel(PreferredNavigationMode mode) {
    switch (mode) {
      case PreferredNavigationMode.auto:
        return 'Automatique';
      case PreferredNavigationMode.keyboard:
        return 'Clavier';
      case PreferredNavigationMode.touch:
        return 'Tactile';
    }
  }

  void _pickColor(bool isFirstColor) {
    // Color picker implementation would go here
    // For now, cycling through some predefined colors
    final colors = [Colors.black, Colors.white, Colors.red, Colors.blue, Colors.green];
    final currentColor = isFirstColor ? _testColor1 : _testColor2;
    final currentIndex = colors.indexOf(currentColor);
    final nextIndex = (currentIndex + 1) % colors.length;
    
    setState(() {
      if (isFirstColor) {
        _testColor1 = colors[nextIndex];
      } else {
        _testColor2 = colors[nextIndex];
      }
      _calculateContrastRatio();
    });
  }

  Future<void> _applyVisionPreset(AccessibilityService service) async {
    await service.updateFontScale(1.5);
    await service.toggleHighContrast();
    await service.enableScreenReader(true);
    await service.updateReducedMotion(true);
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Préréglage Vision appliqué')),
      );
    }
  }

  Future<void> _applyMotorPreset(AccessibilityService service) async {
    await service.updateKeyboardNavigation(true);
    await service.updateReducedMotion(true);
    await service.updateFontScale(1.2);
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Préréglage Motricité appliqué')),
      );
    }
  }

  Future<void> _applyCognitivePreset(AccessibilityService service) async {
    await service.updateReducedMotion(true);
    await service.updateFontScale(1.3);
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Préréglage Cognition appliqué')),
      );
    }
  }

  void _showResetDialog(AccessibilityService service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Réinitialiser les paramètres'),
        content: const Text(
          'Êtes-vous sûr de vouloir réinitialiser tous les paramètres d\'accessibilité aux valeurs par défaut ?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              await service.resetToDefaults();
              if (mounted) {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Paramètres réinitialisés')),
                );
              }
            },
            child: const Text('Réinitialiser'),
          ),
        ],
      ),
    );
  }

  void _showAccessibilityHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Aide sur l\'accessibilité'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHelpSection('Taille de police', 'Ajustez la taille du texte pour améliorer la lisibilité.'),
              _buildHelpSection('Contraste élevé', 'Améliore la distinction entre les couleurs.'),
              _buildHelpSection('Lecteur d\'écran', 'Active la synthèse vocale pour les éléments de l\'interface.'),
              _buildHelpSection('Navigation clavier', 'Permet de naviguer avec les touches du clavier.'),
              _buildHelpSection('Mouvement réduit', 'Réduit les animations pour éviter les distractions.'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpSection(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Text(description),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getAssessmentQuestions() {
    return [
      {
        'id': 'vision',
        'question': 'Avez-vous des difficultés à lire le texte à l\'écran ?',
        'options': ['Jamais', 'Parfois', 'Souvent', 'Toujours'],
      },
      {
        'id': 'hearing',
        'question': 'Avez-vous des difficultés à entendre les sons de l\'application ?',
        'options': ['Jamais', 'Parfois', 'Souvent', 'Toujours'],
      },
      {
        'id': 'motor',
        'question': 'Avez-vous des difficultés à utiliser l\'écran tactile ?',
        'options': ['Jamais', 'Parfois', 'Souvent', 'Toujours'],
      },
      {
        'id': 'cognitive',
        'question': 'Trouvez-vous l\'interface trop complexe ou distrayante ?',
        'options': ['Jamais', 'Parfois', 'Souvent', 'Toujours'],
      },
      {
        'id': 'navigation',
        'question': 'Préférez-vous utiliser le clavier pour naviguer ?',
        'options': ['Jamais', 'Parfois', 'Souvent', 'Toujours'],
      },
    ];
  }

  List<Map<String, dynamic>> _generateRecommendations() {
    final recommendations = <Map<String, dynamic>>[];
    
    // Analyze answers and generate recommendations
    if (_assessmentAnswers['vision'] == 'Souvent' || _assessmentAnswers['vision'] == 'Toujours') {
      recommendations.add({
        'icon': Icons.text_fields,
        'title': 'Augmenter la taille de police',
        'description': 'Recommandé: 150% de la taille normale',
        'action': () => _accessibilityService.updateFontScale(1.5),
      });
      
      recommendations.add({
        'icon': Icons.contrast,
        'title': 'Activer le contraste élevé',
        'description': 'Améliore la lisibilité du texte',
        'action': () => _accessibilityService.toggleHighContrast(),
      });
    }
    
    if (_assessmentAnswers['hearing'] == 'Souvent' || _assessmentAnswers['hearing'] == 'Toujours') {
      recommendations.add({
        'icon': Icons.vibration,
        'title': 'Activer le retour haptique',
        'description': 'Remplace les signaux sonores par des vibrations',
        'action': () => {}, // Would implement haptic feedback settings
      });
    }
    
    if (_assessmentAnswers['motor'] == 'Souvent' || _assessmentAnswers['motor'] == 'Toujours') {
      recommendations.add({
        'icon': Icons.keyboard,
        'title': 'Activer la navigation clavier',
        'description': 'Permet de naviguer sans utiliser l\'écran tactile',
        'action': () => _accessibilityService.updateKeyboardNavigation(true),
      });
    }
    
    if (_assessmentAnswers['cognitive'] == 'Souvent' || _assessmentAnswers['cognitive'] == 'Toujours') {
      recommendations.add({
        'icon': Icons.motion_photos_off,
        'title': 'Réduire les mouvements',
        'description': 'Diminue les animations et transitions',
        'action': () => _accessibilityService.updateReducedMotion(true),
      });
    }
    
    return recommendations;
  }

  Future<void> _exportSettings(AccessibilityService service) async {
    try {
      final preferences = service.getAccessibilityPreferences();
      final json = jsonEncode(preferences.toJson());
      
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/accessibility_settings.json');
      await file.writeAsString(json);
      
      await Share.shareXFiles([XFile(file.path)], text: 'Paramètres d\'accessibilité');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Paramètres exportés avec succès')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de l\'export: $e')),
        );
      }
    }
  }

  Future<void> _importSettings(AccessibilityService service) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );
      
      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final content = await file.readAsString();
        final json = jsonDecode(content);
        
        final preferences = AccessibilityPreferences.fromJson(json);
        
        // Apply imported settings
        await service.updateFontScale(preferences.fontSize);
        if (preferences.highContrastMode != service.isHighContrastEnabled) {
          await service.toggleHighContrast();
        }
        await service.enableScreenReader(preferences.screenReaderEnabled);
        await service.updateKeyboardNavigation(preferences.keyboardNavigationEnabled);
        await service.updateReducedMotion(preferences.reducedMotion);
        await service.updateNavigationMode(preferences.preferredNavigationMode);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Paramètres importés avec succès')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de l\'import: $e')),
        );
      }
    }
  }

  Future<void> _exportAssessmentResults() async {
    try {
      final results = {
        'timestamp': DateTime.now().toIso8601String(),
        'answers': _assessmentAnswers,
        'recommendations': _generateRecommendations().map((r) => {
          'title': r['title'],
          'description': r['description'],
        }).toList(),
      };
      
      final json = jsonEncode(results);
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/accessibility_assessment.json');
      await file.writeAsString(json);
      
      await Share.shareXFiles([XFile(file.path)], text: 'Résultats de l\'évaluation d\'accessibilité');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Résultats exportés avec succès')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de l\'export: $e')),
        );
      }
    }
  }

  Future<void> _createBackup(AccessibilityService service) async {
    try {
      final preferences = service.getAccessibilityPreferences();
      final backup = {
        'timestamp': DateTime.now().toIso8601String(),
        'version': '1.0',
        'preferences': preferences.toJson(),
      };
      
      final json = jsonEncode(backup);
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/accessibility_backup_${DateTime.now().millisecondsSinceEpoch}.json');
      await file.writeAsString(json);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Sauvegarde créée avec succès')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la sauvegarde: $e')),
        );
      }
    }
  }

  Future<void> _runDiagnostics(AccessibilityService service) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('Exécution des diagnostics...'),
          ],
        ),
      ),
    );
    
    // Simulate diagnostics
    await Future.delayed(const Duration(seconds: 2));
    
    if (mounted) {
      Navigator.of(context).pop();
      
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Résultats des diagnostics'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDiagnosticItem('Lecteur d\'écran', 'Fonctionnel', true),
              _buildDiagnosticItem('Navigation clavier', 'Fonctionnel', true),
              _buildDiagnosticItem('Retour haptique', 'Fonctionnel', true),
              _buildDiagnosticItem('Contraste', 'Optimal', true),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Fermer'),
            ),
          ],
        ),
      );
    }
  }
}