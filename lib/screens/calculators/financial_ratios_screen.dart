import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../widgets/calculators/financial_ratios_calculator.dart';
import '../../providers/calculator_providers.dart';
import '../../models/calculators/calculation_history_item.dart';
import '../../theme/design_tokens.dart';

class FinancialRatiosScreen extends ConsumerStatefulWidget {
  const FinancialRatiosScreen({super.key});

  @override
  ConsumerState<FinancialRatiosScreen> createState() => _FinancialRatiosScreenState();
}

class _FinancialRatiosScreenState extends ConsumerState<FinancialRatiosScreen> {
  bool _showHelp = false;

  @override
  void initState() {
    super.initState();
    // Set active calculator
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(activeCalculatorProvider.notifier).state = CalculatorType.financialRatios;
    });
  }

  @override
  Widget build(BuildContext context) {
    final calculatorState = ref.watch(calculatorStateProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Ratios Financiers'),
        backgroundColor: DesignTokens.colorPrimary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_showHelp ? Icons.help : Icons.help_outline),
            onPressed: () {
              setState(() {
                _showHelp = !_showHelp;
              });
            },
          ),
          if (calculatorState.canExport)
            PopupMenuButton<String>(
              icon: const Icon(Icons.file_download),
              onSelected: _handleExport,
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'pdf',
                  child: ListTile(
                    leading: Icon(Icons.picture_as_pdf),
                    title: Text('Exporter en PDF'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'excel',
                  child: ListTile(
                    leading: Icon(Icons.table_chart),
                    title: Text('Exporter en Excel'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'clear',
                child: ListTile(
                  leading: Icon(Icons.clear),
                  title: Text('Effacer'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'history',
                child: ListTile(
                  leading: Icon(Icons.history),
                  title: Text('Historique'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'about',
                child: ListTile(
                  leading: Icon(Icons.info),
                  title: Text('À propos'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          if (_showHelp) _buildHelpSection(),
          Expanded(
            child: const FinancialRatiosCalculator(),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb, color: Colors.blue[700]),
              const SizedBox(width: 8),
              Text(
                'Guide d\'utilisation',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.blue[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildHelpItem(
            'Étape 1',
            'Saisissez les données du bilan (actifs, passifs, capitaux propres)',
          ),
          _buildHelpItem(
            'Étape 2',
            'Complétez les données du compte de résultat (CA, résultats, charges)',
          ),
          _buildHelpItem(
            'Étape 3',
            'Consultez les ratios calculés et l\'analyse automatique',
          ),
          const SizedBox(height: 8),
          Text(
            'Les ratios sont automatiquement interprétés avec des recommandations personnalisées.',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.blue[600],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpItem(String step, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.blue[700],
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                step.split(' ')[1],
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  step,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.blue[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleExport(String format) async {
    try {
      final exportActions = ref.read(calculatorExportProvider);
      await exportActions.exportFinancialRatios(format: format);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export $format réussi'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'export: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'clear':
        _showClearConfirmation();
        break;
      case 'history':
        _navigateToHistory();
        break;
      case 'about':
        _showAboutDialog();
        break;
    }
  }

  void _showClearConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Effacer les données'),
        content: const Text('Voulez-vous vraiment effacer toutes les données saisies ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(calculatorActionsProvider).clearFinancialRatiosResults();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Données effacées')),
              );
            },
            child: const Text('Effacer', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _navigateToHistory() {
    Navigator.pushNamed(context, '/calculation-history');
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Calculateur de Ratios Financiers'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Ce calculateur analyse la santé financière de votre entreprise à travers quatre catégories de ratios :',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              Text('• Liquidité : Capacité à honorer les dettes à court terme'),
              Text('• Rentabilité : Performance économique et financière'),
              Text('• Activité : Efficacité dans l\'utilisation des actifs'),
              Text('• Endettement : Structure financière et solvabilité'),
              SizedBox(height: 16),
              Text(
                'Les résultats incluent une analyse automatique et des recommandations personnalisées.',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }
}
