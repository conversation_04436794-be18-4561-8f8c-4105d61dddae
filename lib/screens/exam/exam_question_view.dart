import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:provider/provider.dart' as provider; // Renamed import
import '../../providers/exam/exam_state_provider.dart';
import '../../services/theme_service.dart'; // Direct import

class ExamQuestionView extends ConsumerWidget {
  const ExamQuestionView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final examState = ref.watch(examStateProvider);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    // Use provider_pkg for Provider, but ThemeService is directly imported
    final isDark = provider.Provider.of<ThemeService>(context, listen: false).isDarkMode;

    if (examState == null) {
      // Should ideally not happen if ExamScreen handles loading
      return Center(
        child: Text(
          "Chargement de l'examen...",
          style: textTheme.bodyLarge?.copyWith(
            color: colorScheme.onSurface,
          ),
        ),
      );
    }

    final question = examState.currentQuestion;
    final selectedAnswerIndex = examState.userAnswers[examState.currentQuestionIndex];

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Question Number and Progress
          Text(
            'Question ${examState.currentQuestionIndex + 1} / ${examState.totalQuestions}',
            style: textTheme.titleMedium?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (examState.currentQuestionIndex + 1) / examState.totalQuestions,
            minHeight: 8,
            borderRadius: BorderRadius.circular(4),
            backgroundColor: colorScheme.surfaceContainerHighest.withValues(alpha: isDark ? 0.4 : 0.6),
            valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
          ),
          const SizedBox(height: 24),

          // Question Text
          Text(
            question.questionText,
            style: textTheme.headlineSmall?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 24),

          // Answer Options
          Expanded(
            child: ListView.builder(
              itemCount: question.options.length,
              itemBuilder: (context, index) {
                final isSelected = selectedAnswerIndex == index;
                
                return Card(
                  elevation: isSelected ? (isDark ? 1 : 4) : (isDark ? 0 : 1),
                  margin: const EdgeInsets.symmetric(vertical: 6),
                  color: isSelected 
                    ? colorScheme.primaryContainer.withValues(alpha: isDark ? 0.3 : 0.4) 
                    : colorScheme.surfaceContainerHighest.withValues(alpha: isDark ? 0.3 : 0.6),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: BorderSide(
                      color: isSelected 
                        ? colorScheme.primary 
                        : isDark 
                          ? colorScheme.outline.withValues(alpha: 0.2) 
                          : Colors.transparent,
                      width: isSelected ? 1.5 : isDark ? 0.5 : 0,
                    ),
                  ),
                  child: ListTile(
                    title: Text(
                      question.options[index], 
                      style: textTheme.bodyLarge?.copyWith(
                        color: colorScheme.onSurface,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                    leading: CircleAvatar(
                      backgroundColor: isSelected
                        ? colorScheme.primary
                        : colorScheme.secondaryContainer,
                      foregroundColor: isSelected
                        ? colorScheme.onPrimary
                        : colorScheme.onSecondaryContainer,
                      child: Text(String.fromCharCode(65 + index)), // A, B, C...
                    ),
                    onTap: () {
                      ref.read(examStateProvider.notifier).selectAnswer(index);
                    },
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16),

          // Navigation Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton.icon(
                onPressed: examState.isFirstQuestion
                    ? null
                    : () => ref.read(examStateProvider.notifier).previousQuestion(),
                icon: const Icon(Icons.arrow_back),
                label: const Text('Précédent'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.surfaceContainerHighest,
                  foregroundColor: colorScheme.onSurfaceVariant,
                  disabledBackgroundColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                  disabledForegroundColor: colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
                  elevation: isDark ? 0 : 2,
                ),
              ),
              if (examState.isLastQuestion)
                ElevatedButton.icon(
                  onPressed: () {
                    // Show confirmation dialog before submitting
                    _showSubmitConfirmationDialog(context, ref);
                  },
                  icon: const Icon(Icons.check_circle_outline),
                  label: const Text('Soumettre'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: colorScheme.onPrimary,
                    elevation: isDark ? 0 : 2,
                  ),
                )
              else
                ElevatedButton.icon(
                  onPressed: () => ref.read(examStateProvider.notifier).nextQuestion(),
                  icon: const Icon(Icons.arrow_forward),
                  label: const Text('Suivant'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: colorScheme.onPrimary,
                    elevation: isDark ? 0 : 2,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  void _showSubmitConfirmationDialog(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    // Use listen: false to prevent provider errors outside of build
    final isDark = provider.Provider.of<ThemeService>(context, listen: false).isDarkMode;
    
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: colorScheme.surface,
          surfaceTintColor: colorScheme.surfaceTint,
          elevation: isDark ? 0 : 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: isDark 
              ? BorderSide(color: colorScheme.outline.withValues(alpha: 0.2)) 
              : BorderSide.none,
          ),
          title: Text(
            'Confirmer la soumission',
            style: textTheme.titleLarge?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'Êtes-vous sûr de vouloir soumettre vos réponses ?',
            style: textTheme.bodyLarge?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                'Annuler',
                style: TextStyle(color: colorScheme.primary),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            FilledButton(
              style: FilledButton.styleFrom(
                backgroundColor: colorScheme.primary,
              ),
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
                ref.read(examStateProvider.notifier).submitExam(); // Submit the exam
              },
              child: Text(
                'Soumettre',
                style: TextStyle(color: colorScheme.onPrimary),
              ),
            ),
          ],
        );
      },
    );
  }
}
