import 'package:flutter/material.dart';
import 'sections/achevement_section.dart';
import 'sections/benefice_avancement_section.dart';
import 'sections/avancement_section.dart';
import 'sections/exercices_section.dart';

class ContratsTermeScreen extends StatefulWidget {
  const ContratsTermeScreen({super.key});

  @override
  State<ContratsTermeScreen> createState() => _ContratsTermeScreenState();
}

class _ContratsTermeScreenState extends State<ContratsTermeScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
              colorScheme.surface,
            ],
          ),
        ),
        child: NestedScrollView(
          headerSliverBuilder: (context, innerBoxIsScrolled) => [
            SliverAppBar(
              title: Text(
                'Les contrats à terme',
                style: textTheme.headlineSmall?.copyWith(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.bold,
                ),
              ),
              centerTitle: true,
              pinned: true,
              floating: true,
              surfaceTintColor: Colors.transparent,
              backgroundColor: colorScheme.surface.withValues(alpha: 0.8),
              bottom: TabBar(
                controller: _tabController,
                isScrollable: true,
                labelStyle: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                unselectedLabelStyle: textTheme.titleMedium,
                labelColor: colorScheme.primary,
                unselectedLabelColor: colorScheme.onSurfaceVariant,
                indicatorColor: colorScheme.primary,
                dividerColor: Colors.transparent,
                tabs: [
                  Tab(
                    icon: Icon(
                      Icons.check_circle_outline,
                      color: _tabController.index == 0
                          ? colorScheme.primary
                          : colorScheme.onSurfaceVariant,
                    ),
                    text: 'Méthode de l\'achèvement',
                  ),
                  Tab(
                    icon: Icon(
                      Icons.trending_up,
                      color: _tabController.index == 1
                          ? colorScheme.primary
                          : colorScheme.onSurfaceVariant,
                    ),
                    text: 'Bénéfice à l\'avancement',
                  ),
                  Tab(
                    icon: Icon(
                      Icons.show_chart,
                      color: _tabController.index == 2
                          ? colorScheme.primary
                          : colorScheme.onSurfaceVariant,
                    ),
                    text: 'Avancement des travaux',
                  ),
                  Tab(
                    icon: Icon(
                      Icons.assignment,
                      color: _tabController.index == 3
                          ? colorScheme.primary
                          : colorScheme.onSurfaceVariant,
                    ),
                    text: 'Exercices',
                  ),
                ],
              ),
            ),
          ],
          body: TabBarView(
            controller: _tabController,
            children: const [
              AchevementSection(),
              BeneficeAvancementSection(),
              AvancementSection(),
              ExercicesSection(),
            ],
          ),
        ),
      ),
    );
  }
}
