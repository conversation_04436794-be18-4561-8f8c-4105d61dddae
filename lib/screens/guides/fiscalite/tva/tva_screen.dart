import 'package:flutter/material.dart';
import 'sections/nouveautes_section.dart';
import 'sections/taux_exonerations_section.dart';
import 'sections/retenue_source_section.dart';
import 'sections/calculateur_section.dart';
import 'sections/exercices_tva_section.dart';
import 'sections/prorata_tva_section.dart';
import 'sections/remboursement_tva_section.dart';

class TvaScreen extends StatelessWidget {
  const TvaScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return DefaultTabController(
      length: 7,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            'TVA',
            style: textTheme.titleLarge?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: colorScheme.surface,
          elevation: 0,
          bottom: TabBar(
            isScrollable: true,
            labelColor: colorScheme.primary,
            unselectedLabelColor: colorScheme.onSurfaceVariant,
            indicatorColor: colorScheme.primary,
            dividerColor: colorScheme.outlineVariant,
            tabs: [
              Tab(
                icon: Icon(Icons.new_releases, color: colorScheme.primary),
                text: 'Nouveautés 2025',
              ),
              Tab(
                icon: Icon(Icons.percent, color: colorScheme.primary),
                text: 'Taux & Exonérations',
              ),
              Tab(
                icon: Icon(Icons.account_balance, color: colorScheme.primary),
                text: 'Retenue à la Source',
              ),
              Tab(
                icon: Icon(Icons.calculate, color: colorScheme.primary),
                text: 'Calculateur',
              ),
              Tab(
                icon: Icon(Icons.pie_chart, color: colorScheme.primary),
                text: 'Prorata',
              ),
              Tab(
                icon: Icon(Icons.school, color: colorScheme.primary),
                text: 'Exercices',
              ),
              Tab(
                icon: Icon(Icons.refresh, color: colorScheme.primary),
                text: 'Remboursement',
              ),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            NouveautesSection(),
            TauxExonerationsSection(),
            RetenueSectionSection(),
            CalculateurSection(),
            ProrataTVASection(),
            ExercicesTvaSection(),
            RemboursementTvaSection(),
          ],
        ),
      ),
    );
  }
}
