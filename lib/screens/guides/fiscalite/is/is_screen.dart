import 'package:flutter/material.dart';
import '../../../../widgets/guide/guide_scaffold.dart';
import '../../../../widgets/guide/breadcrumb_navigation.dart';

class IsScreen extends StatelessWidget {
  const IsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final breadcrumbs = BreadcrumbBuilder.buildFiscaliteBreadcrumbs(
      fiscaliteType: 'IS',
      onFiscaliteNavigate: () {
        Navigator.of(context).popUntil((route) => route.isFirst);
      },
    );

    return GuideScaffold(
      guideId: 'is',
      title: 'Impôt sur les Sociétés',
      breadcrumbs: breadcrumbs,
      showSearch: true,
      showProgress: true,
      allowMultipleExpanded: false,
      onSectionCompleted: (sectionId) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Section "$sectionId" terminée !'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      },
      onBackPressed: () {
        Navigator.of(context).pop();
      },
    );
  }
}
