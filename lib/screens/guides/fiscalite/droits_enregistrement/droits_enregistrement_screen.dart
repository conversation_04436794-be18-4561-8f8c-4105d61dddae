import 'package:flutter/material.dart';
import '../../../../widgets/guide/guide_scaffold.dart';
import '../../../../widgets/guide/breadcrumb_navigation.dart';

class DroitsEnregistrementScreen extends StatelessWidget {
  const DroitsEnregistrementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final breadcrumbs = BreadcrumbBuilder.buildFiscaliteBreadcrumbs(
      fiscaliteType: 'Droits d\'Enregistrement',
      onFiscaliteNavigate: () {
        Navigator.of(context).popUntil((route) => route.isFirst);
      },
    );

    return GuideScaffold(
      guideId: 'droits_enregistrement',
      title: 'Droits d\'Enregistrement',
      breadcrumbs: breadcrumbs,
      showSearch: true,
      showProgress: true,
      allowMultipleExpanded: false,
      onSectionCompleted: (sectionId) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Section "$sectionId" terminée !'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      },
      onBackPressed: () {
        Navigator.of(context).pop();
      },
    );
  }
}
