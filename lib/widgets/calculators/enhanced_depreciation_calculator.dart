import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/calculators/enhanced_depreciation_data.dart';
import '../../services/enhanced_depreciation_service.dart';
import '../../theme/design_tokens.dart';
import '../custom_text_field.dart';

class EnhancedDepreciationCalculator extends ConsumerStatefulWidget {
  const EnhancedDepreciationCalculator({super.key});

  @override
  ConsumerState<EnhancedDepreciationCalculator> createState() => _EnhancedDepreciationCalculatorState();
}

class _EnhancedDepreciationCalculatorState extends ConsumerState<EnhancedDepreciationCalculator>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  
  // Form Controllers
  final _assetNameController = TextEditingController();
  final _assetCostController = TextEditingController();
  final _residualValueController = TextEditingController();
  final _usefulLifeController = TextEditingController();
  final _degressiveCoefficientController = TextEditingController();
  final _totalUnitsController = TextEditingController();
  
  DepreciationMethod _selectedMethod = DepreciationMethod.linear;
  DateTime _acquisitionDate = DateTime.now();
  bool _midYearConvention = false;
  bool _comparisonMode = false;
  AssetPreset? _selectedPreset;
  
  EnhancedDepreciationResult? _result;
  List<DepreciationComparison>? _comparisons;
  bool _isCalculating = false;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    _disposeControllers();
    super.dispose();
  }
  
  void _disposeControllers() {
    _assetNameController.dispose();
    _assetCostController.dispose();
    _residualValueController.dispose();
    _usefulLifeController.dispose();
    _degressiveCoefficientController.dispose();
    _totalUnitsController.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildInputTab(),
                _buildResultsTab(),
                _buildComparisonTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildCalculateButton(),
    );
  }
  
  Widget _buildTabBar() {
    return Container(
      color: DesignTokens.colorSurface,
      child: TabBar(
        controller: _tabController,
        labelColor: DesignTokens.colorPrimary,
        unselectedLabelColor: DesignTokens.colorOnSurfaceVariant,
        indicatorColor: DesignTokens.colorPrimary,
        tabs: const [
          Tab(
            icon: Icon(Icons.input),
            text: 'Saisie',
          ),
          Tab(
            icon: Icon(Icons.table_chart),
            text: 'Résultats',
          ),
          Tab(
            icon: Icon(Icons.compare),
            text: 'Comparaison',
          ),
        ],
      ),
    );
  }
  
  Widget _buildInputTab() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAssetPresetSection(),
            const SizedBox(height: 16),
            _buildAssetDetailsSection(),
            const SizedBox(height: 16),
            _buildMethodSelectionSection(),
            const SizedBox(height: 16),
            _buildOptionsSection(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAssetPresetSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Modèles d\'Actifs',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<AssetPreset>(
              value: _selectedPreset,
              decoration: const InputDecoration(
                labelText: 'Sélectionner un modèle (optionnel)',
                border: OutlineInputBorder(),
              ),
              items: AssetPreset.commonPresets.map((preset) {
                return DropdownMenuItem(
                  value: preset,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(preset.name),
                      Text(
                        preset.description,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (preset) {
                setState(() {
                  _selectedPreset = preset;
                  if (preset != null) {
                    _applyPreset(preset);
                  }
                });
              },
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAssetDetailsSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Détails de l\'Actif',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _assetNameController,
              label: 'Nom de l\'actif',
              validator: (value) => value?.isEmpty == true ? 'Ce champ est requis' : null,
            ),
            const SizedBox(height: 12),
            CustomTextField(
              controller: _assetCostController,
              label: 'Coût d\'acquisition (DH)',
              keyboardType: TextInputType.number,
              validator: _validatePositiveNumber,
            ),
            const SizedBox(height: 12),
            CustomTextField(
              controller: _residualValueController,
              label: 'Valeur résiduelle (DH)',
              keyboardType: TextInputType.number,
              validator: _validateNumber,
            ),
            const SizedBox(height: 12),
            CustomTextField(
              controller: _usefulLifeController,
              label: 'Durée d\'utilité (années)',
              keyboardType: TextInputType.number,
              validator: _validatePositiveInteger,
            ),
            const SizedBox(height: 12),
            ListTile(
              title: const Text('Date d\'acquisition'),
              subtitle: Text(_formatDate(_acquisitionDate)),
              trailing: const Icon(Icons.calendar_today),
              onTap: _selectAcquisitionDate,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildMethodSelectionSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Méthode d\'Amortissement',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 16),
            ...DepreciationMethod.values.map((method) {
              return RadioListTile<DepreciationMethod>(
                title: Text(method.displayName),
                subtitle: Text(method.description),
                value: method,
                groupValue: _selectedMethod,
                onChanged: (value) {
                  setState(() {
                    _selectedMethod = value!;
                  });
                },
              );
            }),
            if (_selectedMethod.requiresCoefficient) ...[
              const SizedBox(height: 12),
              CustomTextField(
                controller: _degressiveCoefficientController,
                label: 'Coefficient dégressif',
                keyboardType: TextInputType.number,
                validator: _validatePositiveNumber,
                hint: '2.0 (par défaut)',
              ),
            ],
            if (_selectedMethod.requiresUnits) ...[
              const SizedBox(height: 12),
              CustomTextField(
                controller: _totalUnitsController,
                label: 'Total d\'unités de production',
                keyboardType: TextInputType.number,
                validator: _validatePositiveNumber,
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildOptionsSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Options',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 12),
            SwitchListTile(
              title: const Text('Convention de mi-année'),
              subtitle: const Text('Amortissement de 6 mois la première année'),
              value: _midYearConvention,
              onChanged: (value) {
                setState(() {
                  _midYearConvention = value;
                });
              },
            ),
            SwitchListTile(
              title: const Text('Mode comparaison'),
              subtitle: const Text('Comparer toutes les méthodes'),
              value: _comparisonMode,
              onChanged: (value) {
                setState(() {
                  _comparisonMode = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildResultsTab() {
    if (_result == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.table_chart_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Configurez l\'amortissement et calculez\npour voir les résultats',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryCard(),
          const SizedBox(height: 16),
          _buildAmortizationTable(),
          if (_result!.taxAdvice != null) ...[
            const SizedBox(height: 16),
            _buildTaxAdviceCard(),
          ],
        ],
      ),
    );
  }
  
  Widget _buildComparisonTab() {
    if (_comparisons == null || _comparisons!.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.compare_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Activez le mode comparaison\npour voir les différentes méthodes',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Comparaison des Méthodes',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: DesignTokens.colorPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ..._comparisons!.map((comparison) => _buildComparisonCard(comparison)),
        ],
      ),
    );
  }
  
  Widget _buildSummaryCard() {
    final summary = _result!.summary;
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Résumé - ${summary.methodDisplayName}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 16),
            _buildSummaryItem('Amortissement total', summary.formattedTotalDepreciation),
            _buildSummaryItem('Valeur résiduelle', summary.formattedRemainingValue),
            _buildSummaryItem('Amortissement annuel moyen', summary.formattedAverageAnnual),
            _buildSummaryItem('Durée totale', '${summary.totalYears} années'),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSummaryItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: DesignTokens.colorPrimary,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildAmortizationTable() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tableau d\'Amortissement',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('Année')),
                  DataColumn(label: Text('Valeur Début')),
                  DataColumn(label: Text('Amortissement')),
                  DataColumn(label: Text('Cumul')),
                  DataColumn(label: Text('Valeur Fin')),
                ],
                rows: _result!.amortizationTable.map((row) {
                  return DataRow(
                    cells: [
                      DataCell(Text(row.year.toString())),
                      DataCell(Text('${row.baseAmount.toStringAsFixed(0)} DH')),
                      DataCell(Text('${row.annuity.toStringAsFixed(0)} DH')),
                      DataCell(Text('${row.cumulativeAnnuity.toStringAsFixed(0)} DH')),
                      DataCell(Text('${row.netBookValue.toStringAsFixed(0)} DH')),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildTaxAdviceCard() {
    final advice = _result!.taxAdvice!;
    
    return Card(
      elevation: 2,
      color: Colors.green[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.lightbulb, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Conseil Fiscal',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Méthode recommandée: ${advice.recommendedMethod}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Économie fiscale estimée: ${advice.estimatedTaxSavings.toStringAsFixed(0)} DH',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(advice.reasoning),
          ],
        ),
      ),
    );
  }
  
  Widget _buildComparisonCard(DepreciationComparison comparison) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              comparison.methodDisplayName,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.colorPrimary,
              ),
            ),
            const SizedBox(height: 12),
            _buildSummaryItem('Amortissement total', '${comparison.totalDepreciation.toStringAsFixed(0)} DH'),
            _buildSummaryItem('Première année', '${comparison.firstYearDepreciation.toStringAsFixed(0)} DH'),
            _buildSummaryItem('Valeur actuelle nette', '${comparison.netPresentValue.toStringAsFixed(0)} DH'),
            _buildSummaryItem('Avantage fiscal', '${comparison.taxBenefit.toStringAsFixed(0)} DH'),
            const SizedBox(height: 8),
            Text(
              comparison.recommendation,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontStyle: FontStyle.italic,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildCalculateButton() {
    return FloatingActionButton.extended(
      onPressed: _isCalculating ? null : _calculateDepreciation,
      backgroundColor: DesignTokens.colorPrimary,
      icon: _isCalculating 
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : const Icon(Icons.calculate),
      label: Text(_isCalculating ? 'Calcul...' : 'Calculer'),
    );
  }
  
  void _applyPreset(AssetPreset preset) {
    _usefulLifeController.text = preset.defaultUsefulLife.toString();
    _selectedMethod = preset.recommendedMethod;
    
    // Calculate residual value if asset cost is provided
    final assetCost = double.tryParse(_assetCostController.text);
    if (assetCost != null) {
      final residualValue = assetCost * (preset.defaultResidualValuePercent / 100);
      _residualValueController.text = residualValue.toStringAsFixed(0);
    }
  }
  
  String? _validateNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Ce champ est requis';
    }
    if (double.tryParse(value) == null) {
      return 'Veuillez saisir un nombre valide';
    }
    return null;
  }
  
  String? _validatePositiveNumber(String? value) {
    final error = _validateNumber(value);
    if (error != null) return error;
    
    final number = double.parse(value!);
    if (number <= 0) {
      return 'La valeur doit être positive';
    }
    return null;
  }
  
  String? _validatePositiveInteger(String? value) {
    final error = _validateNumber(value);
    if (error != null) return error;
    
    final number = int.tryParse(value!);
    if (number == null || number <= 0) {
      return 'Veuillez saisir un nombre entier positif';
    }
    return null;
  }
  
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
  
  Future<void> _selectAcquisitionDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _acquisitionDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    
    if (date != null) {
      setState(() {
        _acquisitionDate = date;
      });
    }
  }
  
  void _calculateDepreciation() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    setState(() {
      _isCalculating = true;
    });
    
    try {
      final input = EnhancedDepreciationInput(
        assetName: _assetNameController.text,
        assetCost: double.parse(_assetCostController.text),
        residualValue: double.parse(_residualValueController.text.isEmpty ? '0' : _residualValueController.text),
        usefulLifeYears: int.parse(_usefulLifeController.text),
        method: _selectedMethod,
        acquisitionDate: _acquisitionDate,
        degressiveCoefficient: _selectedMethod.requiresCoefficient 
            ? double.tryParse(_degressiveCoefficientController.text) ?? 2.0
            : null,
        totalUnits: _selectedMethod.requiresUnits 
            ? double.tryParse(_totalUnitsController.text)
            : null,
        midYearConvention: _midYearConvention,
      );
      
      final service = EnhancedDepreciationService();
      final result = service.calculateDepreciation(input);
      
      List<DepreciationComparison>? comparisons;
      if (_comparisonMode) {
        comparisons = service.compareDepreciationMethods(input);
      }
      
      setState(() {
        _result = result;
        _comparisons = comparisons;
        _isCalculating = false;
      });
      
      // Switch to results tab
      _tabController.animateTo(1);
      
    } catch (e) {
      setState(() {
        _isCalculating = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du calcul: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
