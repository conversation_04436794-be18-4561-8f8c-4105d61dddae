import 'package:hive/hive.dart';

part 'spaced_repetition_item.g.dart';

@HiveType(typeId: 10)
class SpacedRepetitionItem extends HiveObject {
  @HiveField(0)
  String questionId;

  @HiveField(1)
  String categoryName;

  @HiveField(2)
  String topic;

  @HiveField(3)
  bool lastResult; // true for correct, false for incorrect

  @HiveField(4)
  int interval; // days until next review

  @HiveField(5)
  DateTime nextReviewDate;

  @HiveField(6)
  double easinessFactor; // SM-2 algorithm factor (1.3 - 2.5)

  @HiveField(7)
  int repetitions; // number of successful reviews

  @HiveField(8)
  int totalAttempts;

  @HiveField(9)
  int consecutiveCorrect;

  @HiveField(10)
  DateTime createdAt;

  SpacedRepetitionItem({
    required this.questionId,
    required this.categoryName,
    required this.topic,
    required this.lastResult,
    this.interval = 1,
    required this.nextReviewDate,
    this.easinessFactor = 2.5,
    this.repetitions = 0,
    this.totalAttempts = 0,
    this.consecutiveCorrect = 0,
    required this.createdAt,
  });

  // Update item after answering a review question using simplified SM-2 algorithm
  void updateAfterAnswer(bool isCorrect) {
    totalAttempts++;
    lastResult = isCorrect;

    if (isCorrect) {
      consecutiveCorrect++;
      repetitions++;
      
      // Update easiness factor based on performance
      if (consecutiveCorrect >= 2) {
        easinessFactor = (easinessFactor + 0.1).clamp(1.3, 2.5);
      }
      
      // Calculate next interval using SM-2 algorithm
      interval = calculateNextInterval();
    } else {
      // Reset on incorrect answer
      consecutiveCorrect = 0;
      easinessFactor = (easinessFactor - 0.2).clamp(1.3, 2.5);
      
      // Reset interval but don't go below 1 day
      interval = 1;
    }

    // Set next review date
    nextReviewDate = DateTime.now().add(Duration(days: interval));
  }

  // Check if this item is due for review
  bool isDue() {
    return DateTime.now().isAfter(nextReviewDate) || 
           DateTime.now().isAtSameMomentAs(nextReviewDate);
  }

  // Calculate next review interval using simplified SM-2 algorithm
  int calculateNextInterval() {
    if (repetitions == 0) {
      return 1;
    } else if (repetitions == 1) {
      return 6;
    } else {
      // SM-2 formula: I(n) = I(n-1) * EF
      double nextInterval = interval * easinessFactor;
      return nextInterval.round().clamp(1, 365); // Max 1 year
    }
  }

  // Reset progress after multiple failed attempts
  void resetProgress() {
    repetitions = 0;
    consecutiveCorrect = 0;
    interval = 1;
    easinessFactor = 2.5;
    nextReviewDate = DateTime.now().add(Duration(days: 1));
  }

  // Get difficulty adjustment based on performance patterns
  int getDifficultyAdjustment() {
    double successRate = totalAttempts > 0 ? consecutiveCorrect / totalAttempts : 0.0;
    
    if (successRate >= 0.8) {
      return 1; // Increase difficulty
    } else if (successRate <= 0.3) {
      return -1; // Decrease difficulty
    }
    
    return 0; // Keep same difficulty
  }

  // Get performance quality score (0-5 scale for SM-2)
  int getPerformanceQuality() {
    if (consecutiveCorrect >= 3) return 5; // Perfect
    if (consecutiveCorrect >= 2) return 4; // Good
    if (consecutiveCorrect >= 1) return 3; // Satisfactory
    if (totalAttempts > 0 && lastResult) return 2; // Hesitant but correct
    return 1; // Incorrect but remembered
  }

  // Get days until next review
  int getDaysUntilReview() {
    final now = DateTime.now();
    if (nextReviewDate.isBefore(now)) {
      return 0; // Overdue
    }
    return nextReviewDate.difference(now).inDays;
  }

  // Check if item should be graduated (removed from spaced repetition)
  bool shouldGraduate() {
    return consecutiveCorrect >= 5 && 
           interval >= 30 && 
           easinessFactor >= 2.0;
  }

  // Get mastery level (0.0 to 1.0)
  double getMasteryLevel() {
    if (totalAttempts == 0) return 0.0;
    
    double baseScore = consecutiveCorrect / totalAttempts;
    double intervalBonus = (interval / 30.0).clamp(0.0, 1.0) * 0.3;
    double easeBonus = ((easinessFactor - 1.3) / 1.2) * 0.2;
    
    return (baseScore * 0.5 + intervalBonus + easeBonus).clamp(0.0, 1.0);
  }

  @override
  String toString() {
    return 'SpacedRepetitionItem(questionId: $questionId, topic: $topic, '
           'interval: $interval, easinessFactor: $easinessFactor, '
           'consecutiveCorrect: $consecutiveCorrect, nextReview: $nextReviewDate)';
  }

  // Create from question when first added to spaced repetition
  factory SpacedRepetitionItem.fromIncorrectAnswer({
    required String questionId,
    required String categoryName,
    required String topic,
  }) {
    return SpacedRepetitionItem(
      questionId: questionId,
      categoryName: categoryName,
      topic: topic,
      lastResult: false,
      interval: 1,
      nextReviewDate: DateTime.now().add(Duration(days: 1)),
      easinessFactor: 2.5,
      repetitions: 0,
      totalAttempts: 1,
      consecutiveCorrect: 0,
      createdAt: DateTime.now(),
    );
  }

  // Create copy with updated values
  SpacedRepetitionItem copyWith({
    String? questionId,
    String? categoryName,
    String? topic,
    bool? lastResult,
    int? interval,
    DateTime? nextReviewDate,
    double? easinessFactor,
    int? repetitions,
    int? totalAttempts,
    int? consecutiveCorrect,
    DateTime? createdAt,
  }) {
    return SpacedRepetitionItem(
      questionId: questionId ?? this.questionId,
      categoryName: categoryName ?? this.categoryName,
      topic: topic ?? this.topic,
      lastResult: lastResult ?? this.lastResult,
      interval: interval ?? this.interval,
      nextReviewDate: nextReviewDate ?? this.nextReviewDate,
      easinessFactor: easinessFactor ?? this.easinessFactor,
      repetitions: repetitions ?? this.repetitions,
      totalAttempts: totalAttempts ?? this.totalAttempts,
      consecutiveCorrect: consecutiveCorrect ?? this.consecutiveCorrect,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
