import 'package:flutter/material.dart';
import 'design_tokens.dart';
import 'semantic_colors.dart';
import 'app_typography.dart';
import 'accessibility_theme_extension.dart';

class AppTheme {
  // Legacy constants for backward compatibility - use DesignTokens instead
  static const double _borderRadius = DesignTokens.radiusLg;
  static const double _smallBorderRadius = DesignTokens.radiusBase;
  static const double _cardElevation = DesignTokens.elevation2;
  static const double _headerElevation = DesignTokens.elevation4;
  static const double _spacing = DesignTokens.space4;
  static const double _smallSpacing = DesignTokens.space2;

  static ThemeData getThemeData(
    ColorScheme colorScheme, {
    AccessibilityThemeExtension? accessibilityExtension,
  }) {
    // Use the accessibility extension or create default
    final accessibility = accessibilityExtension ?? const AccessibilityThemeExtension();
    
    // Use the new typography system with font scaling
    final textTheme = AppTypography.createTextTheme(colorScheme, fontScale: accessibility.fontScale);

    // Apply high contrast color scheme if enabled
    final effectiveColorScheme = accessibility.highContrastMode
        ? _getHighContrastColorScheme(colorScheme, accessibility)
        : colorScheme;

    return ThemeData(
      useMaterial3: true,
      colorScheme: effectiveColorScheme,
      textTheme: textTheme,
      // Add semantic colors and accessibility extension
      extensions: <ThemeExtension<dynamic>>[
        effectiveColorScheme.brightness == Brightness.dark
            ? SemanticColors.dark
            : SemanticColors.light,
        accessibility,
      ],
      appBarTheme: AppBarTheme(
        centerTitle: true,
        elevation: _headerElevation,
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        surfaceTintColor: Colors.transparent,
        shadowColor: colorScheme.shadow.withValues(alpha: 0.1),
      ),
      navigationRailTheme: NavigationRailThemeData(
        backgroundColor: effectiveColorScheme.surface,
        elevation: _cardElevation,
        selectedIconTheme: IconThemeData(
          color: accessibility.highContrastMode 
              ? accessibility.getHighContrastColor('primary', effectiveColorScheme.brightness)
              : effectiveColorScheme.primary,
          size: accessibility.fontScale >= 1.2 ? 32 : 28,
        ),
        unselectedIconTheme: IconThemeData(
          color: accessibility.highContrastMode
              ? accessibility.getHighContrastColor('onSurface', effectiveColorScheme.brightness)
              : effectiveColorScheme.onSurfaceVariant,
          size: accessibility.fontScale >= 1.2 ? 28 : 24,
        ),
        selectedLabelTextStyle: accessibility.getScaledTextStyle(TextStyle(
          color: accessibility.highContrastMode 
              ? accessibility.getHighContrastColor('primary', effectiveColorScheme.brightness)
              : effectiveColorScheme.primary,
          fontWeight: FontWeight.w600,
          fontSize: 14,
          letterSpacing: 0.1,
        )),
        unselectedLabelTextStyle: accessibility.getScaledTextStyle(TextStyle(
          color: accessibility.highContrastMode
              ? accessibility.getHighContrastColor('onSurface', effectiveColorScheme.brightness)
              : effectiveColorScheme.onSurfaceVariant,
          fontWeight: FontWeight.w500,
          fontSize: 14,
          letterSpacing: 0.1,
        )),
        useIndicator: true,
        indicatorColor: accessibility.highContrastMode
            ? accessibility.getHighContrastColor('primary', effectiveColorScheme.brightness).withValues(alpha: 0.2)
            : effectiveColorScheme.primaryContainer,
        indicatorShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_smallBorderRadius),
          side: accessibility.highContrastMode ? BorderSide(
            color: accessibility.getHighContrastColor('border', effectiveColorScheme.brightness),
            width: 1.0,
          ) : BorderSide.none,
        ),
        minWidth: accessibility.enhancedTouchTargetSize >= 48.0 ? 80 : 72,
        minExtendedWidth: 220,
      ),
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: effectiveColorScheme.surface,
        elevation: _headerElevation,
        shadowColor: effectiveColorScheme.shadow.withValues(alpha: 0.1),
        surfaceTintColor: Colors.transparent,
        indicatorColor: accessibility.highContrastMode
            ? accessibility.getHighContrastColor('primary', effectiveColorScheme.brightness).withValues(alpha: 0.2)
            : effectiveColorScheme.primaryContainer,
        labelTextStyle: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return accessibility.getScaledTextStyle(AppTypography.labelMedium().copyWith(
              color: accessibility.highContrastMode
                  ? accessibility.getHighContrastColor('primary', effectiveColorScheme.brightness)
                  : effectiveColorScheme.primary,
              fontWeight: FontWeight.w600,
            ));
          }
          return accessibility.getScaledTextStyle(AppTypography.labelMedium().copyWith(
            color: accessibility.highContrastMode
                ? accessibility.getHighContrastColor('onSurface', effectiveColorScheme.brightness)
                : effectiveColorScheme.onSurfaceVariant,
          ));
        }),
        iconTheme: WidgetStateProperty.resolveWith((states) {
          final size = accessibility.fontScale >= 1.2 ? 28.0 : 24.0;
          if (states.contains(WidgetState.selected)) {
            return IconThemeData(
              color: accessibility.highContrastMode 
                  ? accessibility.getHighContrastColor('primary', effectiveColorScheme.brightness)
                  : effectiveColorScheme.primary,
              size: size,
            );
          }
          return IconThemeData(
            color: accessibility.highContrastMode
                ? accessibility.getHighContrastColor('onSurface', effectiveColorScheme.brightness)
                : effectiveColorScheme.onSurfaceVariant,
            size: size,
          );
        }),
        height: accessibility.enhancedTouchTargetSize >= 48.0 ? 72.0 : 64.0,
      ),
      cardTheme: CardThemeData(
        elevation: _cardElevation,
        shadowColor: colorScheme.shadow.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
        clipBehavior: Clip.antiAlias,
        color: colorScheme.surface,
      ),
      snackBarTheme: SnackBarThemeData(
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_smallBorderRadius),
        ),
        elevation: _cardElevation,
        backgroundColor: colorScheme.inverseSurface,
        contentTextStyle: AppTypography.bodyMedium().copyWith(
          color: colorScheme.onInverseSurface,
        ),
      ),
      // Removed duplicate elevatedButtonTheme - using the enhanced version below
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: effectiveColorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_smallBorderRadius),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_smallBorderRadius),
          borderSide: BorderSide(
            color: accessibility.getHighContrastColor('focus', effectiveColorScheme.brightness),
            width: accessibility.enhancedFocusIndicators ? 3.0 : 2.0,
          ),
        ),
        enabledBorder: accessibility.highContrastMode ? OutlineInputBorder(
          borderRadius: BorderRadius.circular(_smallBorderRadius),
          borderSide: BorderSide(
            color: accessibility.getHighContrastColor('border', effectiveColorScheme.brightness),
            width: 1.0,
          ),
        ) : null,
        contentPadding: EdgeInsets.symmetric(
          horizontal: DesignTokens.space4,
          vertical: accessibility.enhancedTouchTargetSize >= 48.0 ? DesignTokens.space5 : DesignTokens.space4,
        ),
        hintStyle: AppTypography.bodyMedium().copyWith(
          color: effectiveColorScheme.onSurfaceVariant.withValues(alpha: 0.7),
        ),
      ),
      // Focus styling is handled through individual widget themes
      // Animation theme with reduced motion support
      pageTransitionsTheme: PageTransitionsTheme(
        builders: {
          TargetPlatform.android: accessibility.reducedMotion 
              ? const FadeUpwardsPageTransitionsBuilder()
              : const ZoomPageTransitionsBuilder(),
          TargetPlatform.iOS: accessibility.reducedMotion
              ? const FadeUpwardsPageTransitionsBuilder() 
              : const CupertinoPageTransitionsBuilder(),
          TargetPlatform.macOS: accessibility.reducedMotion
              ? const FadeUpwardsPageTransitionsBuilder()
              : const CupertinoPageTransitionsBuilder(),
          TargetPlatform.windows: accessibility.reducedMotion
              ? const FadeUpwardsPageTransitionsBuilder()
              : const FadeUpwardsPageTransitionsBuilder(),
          TargetPlatform.linux: accessibility.reducedMotion
              ? const FadeUpwardsPageTransitionsBuilder()
              : const FadeUpwardsPageTransitionsBuilder(),
        },
      ),
      // Enhanced button themes with accessibility features
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          minimumSize: Size(accessibility.minimumTouchTargetSize, accessibility.minimumTouchTargetSize),
          padding: EdgeInsets.symmetric(
            horizontal: DesignTokens.space6,
            vertical: accessibility.enhancedTouchTargetSize >= 48.0 ? DesignTokens.space4 : DesignTokens.space3,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_smallBorderRadius),
            side: accessibility.highContrastMode ? BorderSide(
              color: accessibility.getHighContrastColor('border', effectiveColorScheme.brightness),
              width: 1.0,
            ) : BorderSide.none,
          ),
          elevation: _cardElevation,
          shadowColor: effectiveColorScheme.shadow.withValues(alpha: 0.2),
          textStyle: AppTypography.buttonMedium(),
          animationDuration: accessibility.animationDuration,
        ),
      ),
      // Enhanced text button theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          minimumSize: Size(accessibility.minimumTouchTargetSize, accessibility.minimumTouchTargetSize),
          padding: EdgeInsets.symmetric(
            horizontal: DesignTokens.space4,
            vertical: accessibility.enhancedTouchTargetSize >= 48.0 ? DesignTokens.space3 : DesignTokens.space2,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_smallBorderRadius),
          ),
          animationDuration: accessibility.animationDuration,
        ),
      ),
      // Enhanced outlined button theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          minimumSize: Size(accessibility.minimumTouchTargetSize, accessibility.minimumTouchTargetSize),
          padding: EdgeInsets.symmetric(
            horizontal: DesignTokens.space6,
            vertical: accessibility.enhancedTouchTargetSize >= 48.0 ? DesignTokens.space4 : DesignTokens.space3,
          ),
          side: BorderSide(
            color: accessibility.highContrastMode 
                ? accessibility.getHighContrastColor('border', effectiveColorScheme.brightness)
                : effectiveColorScheme.outline,
            width: accessibility.enhancedFocusIndicators ? 2.0 : 1.0,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_smallBorderRadius),
          ),
          animationDuration: accessibility.animationDuration,
        ),
      ),
    );
  }

  static BoxDecoration getGradientDecoration(
    ColorScheme colorScheme, {
    AccessibilityThemeExtension? accessibility,
  }) {
    final accessibilityExt = accessibility ?? const AccessibilityThemeExtension();
    
    // Use solid colors in high contrast mode instead of gradients
    if (accessibilityExt.highContrastMode) {
      return BoxDecoration(
        color: accessibilityExt.getHighContrastColor('primary', colorScheme.brightness),
        borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
        border: Border.all(
          color: accessibilityExt.getHighContrastColor('border', colorScheme.brightness),
          width: 2.0,
        ),
        boxShadow: accessibilityExt.reducedMotion ? null : [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.3),
            blurRadius: DesignTokens.elevation12,
            offset: const Offset(0, 4),
            spreadRadius: 2,
          ),
        ],
      );
    }

    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          colorScheme.primary,
          colorScheme.primary.withValues(alpha: 0.8),
          colorScheme.primaryContainer,
        ],
        stops: const [0.0, 0.5, 1.0],
      ),
      borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
      boxShadow: accessibilityExt.reducedMotion ? null : [
        BoxShadow(
          color: colorScheme.shadow.withValues(alpha: 0.15),
          blurRadius: DesignTokens.elevation12,
          offset: const Offset(0, 4),
          spreadRadius: 2,
        ),
      ],
    );
  }

  static BoxDecoration getModuleIconDecoration(
    ColorScheme colorScheme, 
    Color baseColor, {
    AccessibilityThemeExtension? accessibility,
  }) {
    final accessibilityExt = accessibility ?? const AccessibilityThemeExtension();
    
    if (accessibilityExt.highContrastMode) {
      // Use high contrast colors for better visibility
      final contrastColor = accessibilityExt.generateAccessibleColor(
        baseColor, 
        colorScheme.brightness,
      );
      
      return BoxDecoration(
        color: contrastColor.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
        border: Border.all(
          color: contrastColor,
          width: 2.0,
        ),
      );
    }

    return BoxDecoration(
      color: baseColor.withValues(alpha: 0.12),
      borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
      border: Border.all(
        color: baseColor.withValues(alpha: 0.05),
        width: 1,
      ),
    );
  }

  static List<Color> getModuleColors(ColorScheme colorScheme) {
    return [
      colorScheme.primary,
      colorScheme.secondary,
      colorScheme.tertiary,
      Color.lerp(colorScheme.primary, colorScheme.secondary, 0.5)!,
      Color.lerp(colorScheme.secondary, colorScheme.tertiary, 0.5)!,
    ];
  }

  static BoxDecoration getCardDecoration(
    ColorScheme colorScheme, {
    AccessibilityThemeExtension? accessibility,
  }) {
    final accessibilityExt = accessibility ?? const AccessibilityThemeExtension();
    
    return BoxDecoration(
      color: colorScheme.surface,
      borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
      border: accessibilityExt.highContrastMode ? Border.all(
        color: accessibilityExt.getHighContrastColor('border', colorScheme.brightness),
        width: 1.0,
      ) : null,
      boxShadow: accessibilityExt.reducedMotion ? null : [
        BoxShadow(
          color: colorScheme.shadow.withValues(alpha: 0.08),
          blurRadius: DesignTokens.elevation8,
          offset: const Offset(0, 3),
          spreadRadius: 1,
        ),
      ],
    );
  }

  /// Get focus decoration for interactive elements
  static BoxDecoration getFocusDecoration(
    ColorScheme colorScheme, {
    AccessibilityThemeExtension? accessibility,
  }) {
    final accessibilityExt = accessibility ?? const AccessibilityThemeExtension();
    return accessibilityExt.getFocusIndicatorDecoration(colorScheme.brightness);
  }

  /// Get accessible button decoration with proper touch targets
  static BoxDecoration getAccessibleButtonDecoration(
    ColorScheme colorScheme, {
    AccessibilityThemeExtension? accessibility,
    bool isPressed = false,
    bool isFocused = false,
  }) {
    final accessibilityExt = accessibility ?? const AccessibilityThemeExtension();
    
    return BoxDecoration(
      color: isPressed 
          ? colorScheme.primary.withValues(alpha: 0.12)
          : Colors.transparent,
      borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
      border: isFocused ? Border.all(
        color: accessibilityExt.getHighContrastColor('focus', colorScheme.brightness),
        width: accessibilityExt.enhancedFocusIndicators ? 3.0 : 2.0,
      ) : (accessibilityExt.highContrastMode ? Border.all(
        color: accessibilityExt.getHighContrastColor('border', colorScheme.brightness),
        width: 1.0,
      ) : null),
      boxShadow: isFocused && accessibilityExt.enhancedFocusIndicators ? [
        BoxShadow(
          color: accessibilityExt.getHighContrastColor('focus', colorScheme.brightness).withValues(alpha: 0.3),
          blurRadius: 8.0,
          spreadRadius: 2.0,
        ),
      ] : null,
    );
  }

  static ColorScheme getDarkColorScheme() {
    return ColorScheme.dark(
      primary: const Color(0xFF60A5FA),      // Bright blue - improved contrast
      secondary: const Color(0xFF818CF8),     // Indigo
      tertiary: const Color(0xFFF472B6),     // Pink

      primaryContainer: const Color(0xFF1E3A8A),  // Darker blue - better contrast
      secondaryContainer: const Color(0xFF3730A3), // Darker indigo
      tertiaryContainer: const Color(0xFF9D174D),  // Darker pink

      surface: const Color(0xFF0F172A),       // Darker background - better contrast
      surfaceContainerHighest: const Color(0xFF1E293B),     // Container surface

      error: const Color(0xFFF87171),         // Soft red
      onError: const Color(0xFF000000),       // Black on error for better contrast

      onPrimary: const Color(0xFF000000),     // Black on primary for better contrast
      onSecondary: const Color(0xFF000000),   // Black on secondary
      onTertiary: const Color(0xFF000000),    // Black on tertiary
      onSurface: const Color(0xFFF1F5F9),     // Very light gray - improved contrast
      onSurfaceVariant: const Color(0xFFCBD5E1), // Light gray for variants

      onPrimaryContainer: const Color(0xFFDEEAFF), // Light blue
      onSecondaryContainer: const Color(0xFFE0E7FF), // Light indigo
      onTertiaryContainer: const Color(0xFFFDF2F8), // Light pink

      outline: const Color(0xFF475569),       // Better contrast border
      shadow: const Color(0xFF000000),       // Pure black for shadows
      inverseSurface: const Color(0xFFF8FAFC), // Very light gray
      onInverseSurface: const Color(0xFF0F172A), // Dark background
      inversePrimary: const Color(0xFF2563EB), // Original blue
    );
  }

  static ColorScheme getLightColorScheme() {
    return ColorScheme.light(
      primary: const Color(0xFF2563EB),      // Blue
      secondary: const Color(0xFF4F46E5),     // Indigo
      tertiary: const Color(0xFFDB2777),     // Pink

      primaryContainer: const Color(0xFFDBEAFE),   // Light blue
      secondaryContainer: const Color(0xFFE0E7FF),  // Light indigo
      tertiaryContainer: const Color(0xFFFCE7F3),  // Light pink

      surface: const Color(0xFFFFFFFF),       // Pure white
      surfaceContainerHighest: const Color(0xFFF1F5F9),      // Light gray - better contrast

      error: const Color(0xFFDC2626),         // Red - better contrast
      onError: const Color(0xFFFFFFFF),       // White on error

      onPrimary: const Color(0xFFFFFFFF),     // White on primary
      onSecondary: const Color(0xFFFFFFFF),   // White on secondary
      onTertiary: const Color(0xFFFFFFFF),    // White on tertiary
      onSurface: const Color(0xFF0F172A),     // Very dark gray - improved contrast
      onSurfaceVariant: const Color(0xFF475569), // Medium gray - better contrast

      onPrimaryContainer: const Color(0xFF1E40AF),   // Darker blue
      onSecondaryContainer: const Color(0xFF3730A3), // Darker indigo
      onTertiaryContainer: const Color(0xFF9D174D),  // Darker pink

      outline: const Color(0xFFCBD5E1),       // Better contrast border
      shadow: const Color(0xFF000000),       // Pure black for shadows
      inverseSurface: const Color(0xFF0F172A), // Dark surface
      onInverseSurface: const Color(0xFFF8FAFC), // Light on dark
      inversePrimary: const Color(0xFF60A5FA), // Bright blue
    );
  }

  /// Get high contrast light color scheme with WCAG AAA compliance (7:1 contrast ratio)
  static ColorScheme getHighContrastLightColorScheme() {
    return ColorScheme.light(
      primary: const Color(0xFF000000),       // Pure black for maximum contrast
      secondary: const Color(0xFF000080),     // Navy blue (7.8:1 contrast)
      tertiary: const Color(0xFF8B0000),      // Dark red (7.4:1 contrast)

      primaryContainer: const Color(0xFFE6E6E6),   // Very light gray
      secondaryContainer: const Color(0xFFE0E7FF),  // Light blue
      tertiaryContainer: const Color(0xFFFFE6E6),  // Light red

      surface: const Color(0xFFFFFFFF),       // Pure white
      surfaceContainerHighest: const Color(0xFFF0F0F0),      // Light gray

      error: const Color(0xFF8B0000),         // Dark red (7.4:1 contrast)
      onError: const Color(0xFFFFFFFF),       // White on error

      onPrimary: const Color(0xFFFFFFFF),     // White on black
      onSecondary: const Color(0xFFFFFFFF),   // White on navy
      onTertiary: const Color(0xFFFFFFFF),    // White on dark red
      onSurface: const Color(0xFF000000),     // Pure black on white
      onSurfaceVariant: const Color(0xFF333333), // Dark gray (12.6:1 contrast)

      onPrimaryContainer: const Color(0xFF000000),   // Black on light gray
      onSecondaryContainer: const Color(0xFF000080), // Navy on light blue
      onTertiaryContainer: const Color(0xFF8B0000),  // Dark red on light red

      outline: const Color(0xFF000000),       // Black borders
      shadow: const Color(0xFF000000),       // Pure black for shadows
      inverseSurface: const Color(0xFF000000), // Black surface
      onInverseSurface: const Color(0xFFFFFFFF), // White on black
      inversePrimary: const Color(0xFFFFFFFF), // White
    );
  }

  /// Get high contrast dark color scheme with WCAG AAA compliance (7:1 contrast ratio)
  static ColorScheme getHighContrastDarkColorScheme() {
    return ColorScheme.dark(
      primary: const Color(0xFFFFFFFF),       // Pure white for maximum contrast
      secondary: const Color(0xFF00FFFF),     // Cyan (12.6:1 contrast on black)
      tertiary: const Color(0xFFFF6B6B),      // Light red (7.1:1 contrast on black)

      primaryContainer: const Color(0xFF1A1A1A),   // Very dark gray
      secondaryContainer: const Color(0xFF003333), // Dark cyan
      tertiaryContainer: const Color(0xFF330000),  // Dark red

      surface: const Color(0xFF000000),       // Pure black
      surfaceContainerHighest: const Color(0xFF0F0F0F),      // Very dark gray

      error: const Color(0xFFFF6B6B),         // Light red (7.1:1 contrast)
      onError: const Color(0xFF000000),       // Black on light red

      onPrimary: const Color(0xFF000000),     // Black on white
      onSecondary: const Color(0xFF000000),   // Black on cyan
      onTertiary: const Color(0xFF000000),    // Black on light red
      onSurface: const Color(0xFFFFFFFF),     // Pure white on black
      onSurfaceVariant: const Color(0xFFCCCCCC), // Light gray (15.3:1 contrast)

      onPrimaryContainer: const Color(0xFFFFFFFF),   // White on dark gray
      onSecondaryContainer: const Color(0xFF00FFFF), // Cyan on dark cyan
      onTertiaryContainer: const Color(0xFFFF6B6B),  // Light red on dark red

      outline: const Color(0xFFFFFFFF),       // White borders
      shadow: const Color(0xFF000000),       // Pure black for shadows
      inverseSurface: const Color(0xFFFFFFFF), // White surface
      onInverseSurface: const Color(0xFF000000), // Black on white
      inversePrimary: const Color(0xFF000000), // Black
    );
  }

  /// Apply high contrast colors based on accessibility preferences
  static ColorScheme _getHighContrastColorScheme(
    ColorScheme baseScheme,
    AccessibilityThemeExtension accessibility,
  ) {
    if (!accessibility.highContrastMode) return baseScheme;

    // Use predefined high contrast schemes or apply semantic color overrides
    final highContrastScheme = baseScheme.brightness == Brightness.dark
        ? getHighContrastDarkColorScheme()
        : getHighContrastLightColorScheme();

    // Apply any custom semantic color overrides
    if (accessibility.semanticColorOverrides.isNotEmpty) {
      return _applySemanticColorOverrides(highContrastScheme, accessibility);
    }

    return highContrastScheme;
  }

  /// Apply semantic color overrides to the color scheme
  static ColorScheme _applySemanticColorOverrides(
    ColorScheme baseScheme,
    AccessibilityThemeExtension accessibility,
  ) {
    final brightness = baseScheme.brightness;
    final overrides = accessibility.semanticColorOverrides;

    return baseScheme.copyWith(
      primary: overrides['primary${brightness == Brightness.light ? 'Light' : 'Dark'}'] ?? baseScheme.primary,
      secondary: overrides['secondary${brightness == Brightness.light ? 'Light' : 'Dark'}'] ?? baseScheme.secondary,
      tertiary: overrides['tertiary${brightness == Brightness.light ? 'Light' : 'Dark'}'] ?? baseScheme.tertiary,
      surface: overrides['surface${brightness == Brightness.light ? 'Light' : 'Dark'}'] ?? baseScheme.surface,
      error: overrides['error${brightness == Brightness.light ? 'Light' : 'Dark'}'] ?? baseScheme.error,
      onPrimary: overrides['onPrimary${brightness == Brightness.light ? 'Light' : 'Dark'}'] ?? baseScheme.onPrimary,
      onSecondary: overrides['onSecondary${brightness == Brightness.light ? 'Light' : 'Dark'}'] ?? baseScheme.onSecondary,
      onTertiary: overrides['onTertiary${brightness == Brightness.light ? 'Light' : 'Dark'}'] ?? baseScheme.onTertiary,
      onSurface: overrides['onSurface${brightness == Brightness.light ? 'Light' : 'Dark'}'] ?? baseScheme.onSurface,
      onError: overrides['onError${brightness == Brightness.light ? 'Light' : 'Dark'}'] ?? baseScheme.onError,
      outline: overrides['border${brightness == Brightness.light ? 'Light' : 'Dark'}'] ?? baseScheme.outline,
    );
  }

  /// Get accessible theme data with all accessibility features enabled
  static ThemeData getAccessibleThemeData(
    ColorScheme colorScheme, {
    double fontScale = 1.0,
    bool highContrastMode = false,
    bool reducedMotion = false,
    bool enhancedFocusIndicators = false,
    Map<String, Color> semanticColorOverrides = const {},
  }) {
    final accessibilityExtension = AccessibilityThemeExtension(
      fontScale: fontScale,
      highContrastMode: highContrastMode,
      reducedMotion: reducedMotion,
      enhancedFocusIndicators: enhancedFocusIndicators,
      semanticColorOverrides: semanticColorOverrides,
    );

    return getThemeData(colorScheme, accessibilityExtension: accessibilityExtension);
  }

  /// Validate if a color scheme meets WCAG AAA standards
  static bool validateColorSchemeAccessibility(ColorScheme colorScheme) {
    final accessibility = AccessibilityThemeExtension();
    
    // Check key color combinations for WCAG AAA compliance (7:1 contrast ratio)
    final checks = [
      accessibility.meetsWCAGAAA(colorScheme.surface, colorScheme.onSurface),
      accessibility.meetsWCAGAAA(colorScheme.primary, colorScheme.onPrimary),
      accessibility.meetsWCAGAAA(colorScheme.secondary, colorScheme.onSecondary),
      accessibility.meetsWCAGAAA(colorScheme.error, colorScheme.onError),
      accessibility.meetsWCAGAAA(colorScheme.primaryContainer, colorScheme.onPrimaryContainer),
    ];

    return checks.every((check) => check);
  }
}
